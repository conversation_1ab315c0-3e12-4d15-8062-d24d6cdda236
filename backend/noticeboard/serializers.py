from rest_framework import serializers
from .models import Notice, NoticeAttachment, NoticeHistory
from towers.models import Tower, Unit
from user.models import Member
import datetime


def make_json_serializable(value):
    """
    Convert non-JSON serializable objects to serializable representations
    """
    if isinstance(value, Member):
        return {
            'id': value.id,
            'full_name': value.full_name,
            'email': getattr(value, 'email', None)
        }
    elif isinstance(value, (datetime.datetime, datetime.date, datetime.time)):
        return value.isoformat()
    elif hasattr(value, '__dict__'):
        # For other model instances, return basic representation
        return {
            'id': getattr(value, 'id', None),
            'str': str(value)
        }
    else:
        return value


class NoticeAttachmentSerializer(serializers.ModelSerializer):
    """
    Serializer for notice attachments (images only)
    """
    file_url = serializers.SerializerMethodField()

    class Meta:
        model = NoticeAttachment
        fields = ['id', 'file', 'file_url', 'file_name', 'file_type', 'file_size', 'created_at']
        read_only_fields = ['id', 'created_at', 'file_url']

    def get_file_url(self, obj):
        """
        Get the full URL for the file
        """
        if obj.file:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.file.url)
            return obj.file.url
        return None


class TowerSerializer(serializers.ModelSerializer):
    """
    Simple serializer for Tower data
    """
    
    class Meta:
        model = Tower
        fields = ['id', 'tower_name', 'tower_number']


class UnitSerializer(serializers.ModelSerializer):
    """
    Simple serializer for Unit data
    """
    tower_name = serializers.CharField(source='tower.tower_name', read_only=True)
    
    class Meta:
        model = Unit
        fields = ['id', 'unit_number', 'tower', 'tower_name']


class NoticeHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for notice history
    """
    edited_by_name = serializers.CharField(source='edited_by.full_name', read_only=True)
    changes_display = serializers.SerializerMethodField()

    class Meta:
        model = NoticeHistory
        fields = ['id', 'edited_by', 'edited_by_name', 'edited_at', 'changes', 'changes_display']
        read_only_fields = ['id', 'edited_at']

    def get_changes_display(self, obj):
        """
        Convert changes JSON to a more readable format
        """
        if not obj.changes:
            return {}
        
        # Convert any non-serializable values in changes
        readable_changes = {}
        for field, change_data in obj.changes.items():
            if isinstance(change_data, dict) and 'old' in change_data and 'new' in change_data:
                readable_changes[field] = {
                    'old': make_json_serializable(change_data['old']),
                    'new': make_json_serializable(change_data['new'])
                }
            else:
                readable_changes[field] = make_json_serializable(change_data)
        
        return readable_changes


class NoticeSerializer(serializers.ModelSerializer):
    """
    Full serializer for notice CRUD operations
    """
    creator_name = serializers.CharField(source='creator.full_name', read_only=True)
    attachments = serializers.SerializerMethodField()
    target_towers_data = TowerSerializer(source='target_towers', many=True, read_only=True)
    target_units_data = UnitSerializer(source='target_units', many=True, read_only=True)
    history = NoticeHistorySerializer(many=True, read_only=True)
    
    # Write-only fields for creating/updating relationships
    target_tower_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    target_unit_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False
    )
    
    class Meta:
        model = Notice
        fields = [
            'id', 'title', 'description', 'internal_title', 'creator', 'creator_name', 'post_as',
            'posted_group', 'posted_member', 'group_name', 'member_name',
            'priority', 'label', 'start_date', 'start_time', 'end_date', 'end_time',
            'status', 'views', 'is_pinned', 'manually_expired', 'created_at', 'updated_at',
            'attachments', 'target_towers_data', 'target_units_data', 'history',
            'target_tower_ids', 'target_unit_ids'
        ]
        read_only_fields = ['id', 'creator', 'status', 'views', 'created_at', 'updated_at']

    def create(self, validated_data):
        # Extract many-to-many data
        target_tower_ids = validated_data.pop('target_tower_ids', [])
        target_unit_ids = validated_data.pop('target_unit_ids', [])
        
        # Create the notice
        notice = Notice.objects.create(**validated_data)
        
        # Set many-to-many relationships
        if target_tower_ids:
            towers = Tower.objects.filter(id__in=target_tower_ids)
            notice.target_towers.set(towers)
        
        if target_unit_ids:
            units = Unit.objects.filter(id__in=target_unit_ids)
            notice.target_units.set(units)
        
        return notice

    def update(self, instance, validated_data):
        # Extract many-to-many data
        target_tower_ids = validated_data.pop('target_tower_ids', None)
        target_unit_ids = validated_data.pop('target_unit_ids', None)
        
        # Track changes for history
        changes = {}
        for field, new_value in validated_data.items():
            old_value = getattr(instance, field)
            if old_value != new_value:
                changes[field] = {
                    'old': old_value,
                    'new': new_value
                }
        
        # Update the instance
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update many-to-many relationships
        if target_tower_ids is not None:
            old_towers = list(instance.target_towers.all())
            towers = Tower.objects.filter(id__in=target_tower_ids)
            instance.target_towers.set(towers)
            new_towers = list(instance.target_towers.all())
            if old_towers != new_towers:
                changes['target_towers'] = {
                    'old': [str(t) for t in old_towers],
                    'new': [str(t) for t in new_towers]
                }
        
        if target_unit_ids is not None:
            old_units = list(instance.target_units.all())
            units = Unit.objects.filter(id__in=target_unit_ids)
            instance.target_units.set(units)
            new_units = list(instance.target_units.all())
            if old_units != new_units:
                changes['target_units'] = {
                    'old': [str(u) for u in old_units],
                    'new': [str(u) for u in new_units]
                }
        
        # Create history entry if there were changes
        if changes:
            NoticeHistory.objects.create(
                notice=instance,
                edited_by=self.context['request'].user,
                changes=changes
            )
        
        return instance

    def get_attachments(self, obj):
        """
        Get attachments with proper context for file URLs
        """
        attachments = obj.attachments.all()
        return NoticeAttachmentSerializer(
            attachments,
            many=True,
            context=self.context
        ).data


class NoticeListSerializer(serializers.ModelSerializer):
    """
    Simplified serializer for notice list views
    """
    creator_name = serializers.CharField(source='creator.full_name', read_only=True)
    attachments = serializers.SerializerMethodField()
    attachment_count = serializers.SerializerMethodField()
    target_towers_count = serializers.SerializerMethodField()
    target_units_count = serializers.SerializerMethodField()
    target_units_data = UnitSerializer(source='target_units', many=True, read_only=True)

    class Meta:
        model = Notice
        fields = [
            'id', 'internal_title', 'creator_name', 'post_as',
            'priority', 'label', 'start_date', 'start_time', 'end_date', 'end_time',
            'status', 'views', 'is_pinned', 'manually_expired', 'created_at',
            'attachments', 'attachment_count', 'target_towers_count', 'target_units_count',
            'target_units_data'
        ]

    def get_attachments(self, obj):
        """
        Get attachments with proper context for file URLs
        """
        attachments = obj.attachments.all()
        return NoticeAttachmentSerializer(
            attachments,
            many=True,
            context=self.context
        ).data

    def get_attachment_count(self, obj):
        return obj.attachments.count()
    
    def get_target_towers_count(self, obj):
        return obj.target_towers.count()
    
    def get_target_units_count(self, obj):
        return obj.target_units.count()
